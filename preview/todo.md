心跳，需要的，这个客户端也要支持的吧，先不搞

gateway中接收多帧这里是有问题的，没办法确定某一帧是不是最后一帧
现在的召唤响应叫多帧响应，这是有问题的因为不只是多帧在这里处理，单帧也是在这里处理的，应该改名字就叫响应

/client 127.0.0.1 8080
>>> Sending Type 4 (Local Status Request)
>>> Sent Type 4 Local Status Request (12 bytes): 68 09 00 04 01 06 01 02 F1 01 01 02 
<<< Received 25 bytes, buffer total: 25 bytes
<<< Extracted complete frame of 25 bytes
<<< Processing complete frame: 68 16 00 04 01 07 00 02 F1 01 C9 02 09 10 13 08 19 01 02 03 00 4B 00 00 00 
<<< Parsed IEC103 message: TYP=04, VSQ=01, COT=07, SRC=00, TGT=02, FUN=F1, INF=01
<<< This is a Type 4 local status response
<<< Type 4 Local Status Response:
    Response Time: 2025-08-19 16:09.713
    Device Status: Online (0x01)
    Communication: Normal (0x02)
    Run Mode: Auto (0x03)
    Alarm Status: No Alarm (0x00)
    Load: 75%
<<< Message content: �
<<< Received 17 bytes, buffer total: 17 bytes
<<< Extracted complete frame of 17 bytes
<<< Processing complete frame: 68 0E 00 03 01 03 02 00 F0 03 03 02 09 10 13 08 19 
<<< Parsed IEC103 message: TYP=03, VSQ=01, COT=03, SRC=02, TGT=00, FUN=F0, INF=03
<<< This is a Type 3 timestamp event
<<< Type 3 Timestamp Event:
    Time: 2025-08-19 16:09.515
    Event Type: 03 (from INF field)
<<< Message content: 
^C
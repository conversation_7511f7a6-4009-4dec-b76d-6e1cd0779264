#include "gw104/asdu_type4_gws.hpp"
#include <spdlog/spdlog.h>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

int AsduType4GWS::DirectResponseFromLocal(const base::ProtocolFrame& request_frame,
                                         base::ProtocolFrameList& response_frames) {
    spdlog::info("ASDU Type4: Processing direct local response request, frame_id={}, data_size={}",
                request_frame.frame_id, request_frame.data.size());

    try {
        // 创建响应帧
        base::ProtocolFrame response_frame;
        CreateResponseHeader(request_frame, response_frame);

        // 生成时间戳数据
        std::vector<uint8_t> timestamp_data;
        GenerateTimestampData(timestamp_data);

        // 生成本地状态数据
        std::vector<uint8_t> status_data;
        GenerateLocalStatusData(status_data);

        // 组装响应数据
        response_frame.data.clear();

        // 添加响应类型标识
        response_frame.data.push_back(0x04); // Type4 响应标识

        // 添加时间戳数据
        response_frame.data.insert(response_frame.data.end(),
                                  timestamp_data.begin(), timestamp_data.end());

        // 添加状态数据
        response_frame.data.insert(response_frame.data.end(),
                                  status_data.begin(), status_data.end());

        // 添加到响应列表
        response_frames.push_back(response_frame);

        spdlog::info("ASDU Type4: Successfully generated local response, timestamp_size={}, status_size={}, total_size={}",
                    timestamp_data.size(), status_data.size(), response_frame.data.size());

        return 0; // 成功

    } catch (const std::exception& e) {
        spdlog::error("ASDU Type4: Exception in DirectResponseFromLocal: {}", e.what());
        return -1; // 失败
    }
}

void AsduType4GWS::GenerateTimestampData(std::vector<uint8_t>& timestamp_data) {
    // 获取当前时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    // 转换为本地时间
    std::tm* local_time = std::localtime(&time_t);

    // 按照 IEC104 时间格式组装（7字节时间戳）
    timestamp_data.clear();
    timestamp_data.reserve(7);

    // 毫秒 (2字节)
    uint16_t milliseconds = static_cast<uint16_t>(ms.count());
    timestamp_data.push_back(static_cast<uint8_t>(milliseconds & 0xFF));
    timestamp_data.push_back(static_cast<uint8_t>((milliseconds >> 8) & 0xFF));

    // 分钟 (1字节, 0-59)
    timestamp_data.push_back(static_cast<uint8_t>(local_time->tm_min));

    // 小时 (1字节, 0-23)
    timestamp_data.push_back(static_cast<uint8_t>(local_time->tm_hour));

    // 日 (1字节, 1-31)
    timestamp_data.push_back(static_cast<uint8_t>(local_time->tm_mday));

    // 月 (1字节, 1-12)
    timestamp_data.push_back(static_cast<uint8_t>(local_time->tm_mon + 1));

    // 年 (1字节, 年份-2000)
    int year = local_time->tm_year + 1900;
    timestamp_data.push_back(static_cast<uint8_t>(year - 2000));

    // 记录时间戳信息
    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(4) << year << "-"
        << std::setw(2) << (local_time->tm_mon + 1) << "-"
        << std::setw(2) << local_time->tm_mday << " "
        << std::setw(2) << local_time->tm_hour << ":"
        << std::setw(2) << local_time->tm_min << ":"
        << std::setw(2) << local_time->tm_sec << "."
        << std::setw(3) << milliseconds;

    spdlog::debug("ASDU Type4: Generated timestamp: {}", oss.str());
}

void AsduType4GWS::GenerateLocalStatusData(std::vector<uint8_t>& status_data) {
    // 生成简单的本地状态数据
    status_data.clear();
    status_data.reserve(8);

    // 设备状态字节
    status_data.push_back(0x01); // 设备在线状态

    // 通信状态字节
    status_data.push_back(0x02); // 通信正常

    // 运行模式字节
    status_data.push_back(0x03); // 自动运行模式

    // 告警状态字节
    status_data.push_back(0x00); // 无告警

    // 负载状态（4字节，模拟负载百分比）
    uint32_t load_percent = 75; // 75% 负载
    status_data.push_back(static_cast<uint8_t>(load_percent & 0xFF));
    status_data.push_back(static_cast<uint8_t>((load_percent >> 8) & 0xFF));
    status_data.push_back(static_cast<uint8_t>((load_percent >> 16) & 0xFF));
    status_data.push_back(static_cast<uint8_t>((load_percent >> 24) & 0xFF));

    spdlog::debug("ASDU Type4: Generated status data: device=online, comm=normal, mode=auto, alarm=none, load={}%",
                 load_percent);
}

void AsduType4GWS::CreateResponseHeader(const base::ProtocolFrame& request_frame,
                                       base::ProtocolFrame& response_frame) {
    // 复制基本信息
    response_frame.type = GetSupportedType();
    response_frame.frame_id = request_frame.frame_id;
    response_frame.asdu_addr = request_frame.asdu_addr;

    // 设置为响应帧
    response_frame.cot = 7; // 激活确认 (activation confirmation)

    spdlog::debug("ASDU Type4: Created response header: type={}, frame_id={}, asdu_addr={}, cot={}",
                 response_frame.type, response_frame.frame_id, response_frame.asdu_addr, response_frame.cot);
}

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan